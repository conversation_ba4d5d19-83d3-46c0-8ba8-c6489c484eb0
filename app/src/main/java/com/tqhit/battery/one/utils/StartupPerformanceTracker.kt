package com.tqhit.battery.one.utils

import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * Centralized performance tracking for app startup phases.
 * Monitors timing metrics across the complete cold start flow.
 */
object StartupPerformanceTracker {
    private const val TAG = "StartupPerformanceTracker"
    
    // Performance thresholds (in milliseconds)
    private const val EXCELLENT_THRESHOLD = 250L
    private const val GOOD_THRESHOLD = 500L
    private const val ACCEPTABLE_THRESHOLD = 1000L
    
    // Startup phases
    enum class StartupPhase {
        APP_LAUNCH,
        SPLASH_ACTIVITY_CREATE,
        SPLASH_ACTIVITY_READY,
        LANGUAGE_SELECTION_CREATE,
        LANGUAGE_SELECTION_READY,
        STARTING_ACTIVITY_CREATE,
        STARTING_ACTIVITY_READY,
        MAIN_ACTIVITY_CREATE,
        MAIN_ACTIVITY_READY,
        SERVICES_CRITICAL_START,
        SERVICES_NON_CRITICAL_START,
        FIRST_FRAME_RENDERED
    }
    
    // Timing data storage
    private val phaseTimestamps = ConcurrentHashMap<StartupPhase, Long>()
    private val phaseDurations = ConcurrentHashMap<StartupPhase, Long>()
    private val memorySnapshots = ConcurrentHashMap<StartupPhase, MemorySnapshot>()
    
    // App launch timestamp
    private val appLaunchTime = AtomicLong(0L)
    
    // Memory tracking
    data class MemorySnapshot(
        val usedMemoryMB: Long,
        val totalMemoryMB: Long,
        val freeMemoryMB: Long,
        val timestamp: Long
    )
    
    /**
     * Initialize tracking when app starts
     */
    fun initializeTracking() {
        val currentTime = System.currentTimeMillis()
        appLaunchTime.set(currentTime)
        phaseTimestamps[StartupPhase.APP_LAUNCH] = currentTime
        
        Log.d(TAG, "STARTUP_TIMING: Performance tracking initialized at $currentTime")
        takeMemorySnapshot(StartupPhase.APP_LAUNCH)
    }
    
    /**
     * Mark the start of a startup phase
     */
    fun markPhaseStart(phase: StartupPhase) {
        val currentTime = System.currentTimeMillis()
        phaseTimestamps[phase] = currentTime
        
        val timeSinceLaunch = currentTime - appLaunchTime.get()
        Log.d(TAG, "STARTUP_TIMING: Phase ${phase.name} started at +${timeSinceLaunch}ms")
        
        takeMemorySnapshot(phase)
    }
    
    /**
     * Mark the completion of a startup phase
     */
    fun markPhaseComplete(phase: StartupPhase) {
        val currentTime = System.currentTimeMillis()
        val startTime = phaseTimestamps[phase]
        
        if (startTime != null) {
            val duration = currentTime - startTime
            phaseDurations[phase] = duration
            
            val timeSinceLaunch = currentTime - appLaunchTime.get()
            val performanceRating = getPerformanceRating(duration)
            
            Log.d(TAG, "STARTUP_TIMING: Phase ${phase.name} completed in ${duration}ms (+${timeSinceLaunch}ms total) - $performanceRating")
        } else {
            Log.w(TAG, "STARTUP_TIMING: Phase ${phase.name} completed but no start time recorded")
        }
        
        takeMemorySnapshot(phase)
    }
    
    /**
     * Take a memory snapshot for performance analysis
     */
    private fun takeMemorySnapshot(phase: StartupPhase) {
        try {
            val runtime = Runtime.getRuntime()
            val totalMemory = runtime.totalMemory() / 1024 / 1024
            val freeMemory = runtime.freeMemory() / 1024 / 1024
            val usedMemory = totalMemory - freeMemory
            
            val snapshot = MemorySnapshot(
                usedMemoryMB = usedMemory,
                totalMemoryMB = totalMemory,
                freeMemoryMB = freeMemory,
                timestamp = System.currentTimeMillis()
            )
            
            memorySnapshots[phase] = snapshot
            
            Log.d(TAG, "MEMORY_TRACKING: ${phase.name} - Used: ${usedMemory}MB, Total: ${totalMemory}MB, Free: ${freeMemory}MB")
        } catch (e: Exception) {
            Log.w(TAG, "Failed to take memory snapshot for ${phase.name}", e)
        }
    }
    
    /**
     * Get performance rating for a duration
     */
    private fun getPerformanceRating(durationMs: Long): String {
        return when {
            durationMs <= EXCELLENT_THRESHOLD -> "🟢 EXCELLENT"
            durationMs <= GOOD_THRESHOLD -> "🟡 GOOD"
            durationMs <= ACCEPTABLE_THRESHOLD -> "🟠 ACCEPTABLE"
            else -> "🔴 NEEDS_OPTIMIZATION"
        }
    }
    
    /**
     * Log comprehensive performance summary
     */
    fun logPerformanceSummary() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "========================================")
                Log.d(TAG, "STARTUP_PERFORMANCE_SUMMARY")
                Log.d(TAG, "========================================")
                
                // Calculate total startup time
                val totalStartupTime = System.currentTimeMillis() - appLaunchTime.get()
                Log.d(TAG, "TOTAL_STARTUP_TIME: ${totalStartupTime}ms ${getPerformanceRating(totalStartupTime)}")
                
                // Log individual phase durations
                Log.d(TAG, "PHASE_DURATIONS:")
                StartupPhase.values().forEach { phase ->
                    val duration = phaseDurations[phase]
                    if (duration != null) {
                        Log.d(TAG, "  ${phase.name}: ${duration}ms ${getPerformanceRating(duration)}")
                    }
                }
                
                // Log memory progression
                Log.d(TAG, "MEMORY_PROGRESSION:")
                val launchMemory = memorySnapshots[StartupPhase.APP_LAUNCH]
                val currentMemory = memorySnapshots.values.maxByOrNull { it.timestamp }
                
                if (launchMemory != null && currentMemory != null) {
                    val memoryIncrease = currentMemory.usedMemoryMB - launchMemory.usedMemoryMB
                    Log.d(TAG, "  Launch: ${launchMemory.usedMemoryMB}MB")
                    Log.d(TAG, "  Current: ${currentMemory.usedMemoryMB}MB")
                    Log.d(TAG, "  Increase: +${memoryIncrease}MB")
                }
                
                // Performance recommendations
                logPerformanceRecommendations()
                
                Log.d(TAG, "========================================")
            } catch (e: Exception) {
                Log.e(TAG, "Error generating performance summary", e)
            }
        }
    }
    
    /**
     * Log performance recommendations based on measurements
     */
    private fun logPerformanceRecommendations() {
        Log.d(TAG, "PERFORMANCE_RECOMMENDATIONS:")
        
        // Check for slow phases
        val slowPhases = phaseDurations.filter { it.value > GOOD_THRESHOLD }
        if (slowPhases.isNotEmpty()) {
            Log.d(TAG, "  🔴 Slow phases detected:")
            slowPhases.forEach { (phase, duration) ->
                Log.d(TAG, "    - ${phase.name}: ${duration}ms")
            }
        }
        
        // Check memory usage
        val currentMemory = memorySnapshots.values.maxByOrNull { it.timestamp }
        if (currentMemory != null && currentMemory.usedMemoryMB > 100) {
            Log.d(TAG, "  🟡 High memory usage: ${currentMemory.usedMemoryMB}MB")
        }
        
        // Overall assessment
        val totalTime = System.currentTimeMillis() - appLaunchTime.get()
        when {
            totalTime <= EXCELLENT_THRESHOLD -> {
                Log.d(TAG, "  ✅ Overall performance is excellent")
            }
            totalTime <= GOOD_THRESHOLD -> {
                Log.d(TAG, "  🟡 Overall performance is good - minor optimizations possible")
            }
            else -> {
                Log.d(TAG, "  🔴 Overall performance needs optimization")
            }
        }
    }
    
    /**
     * Get current performance metrics for external use
     */
    fun getCurrentMetrics(): Map<String, Any> {
        val totalTime = System.currentTimeMillis() - appLaunchTime.get()
        val currentMemory = memorySnapshots.values.maxByOrNull { it.timestamp }
        
        return mapOf(
            "totalStartupTimeMs" to totalTime,
            "appLaunchTime" to appLaunchTime.get(),
            "phaseDurations" to phaseDurations.toMap(),
            "currentMemoryMB" to (currentMemory?.usedMemoryMB ?: 0),
            "performanceRating" to getPerformanceRating(totalTime)
        )
    }
    
    /**
     * Reset tracking for new measurement cycle
     */
    fun reset() {
        phaseTimestamps.clear()
        phaseDurations.clear()
        memorySnapshots.clear()
        appLaunchTime.set(0L)
        
        Log.d(TAG, "STARTUP_TIMING: Performance tracking reset")
    }
    
    /**
     * Log a custom timing measurement
     */
    fun logCustomTiming(operation: String, durationMs: Long) {
        val rating = getPerformanceRating(durationMs)
        Log.d(TAG, "CUSTOM_TIMING: $operation took ${durationMs}ms - $rating")
    }
    
    /**
     * Measure and log execution time of a block
     */
    inline fun <T> measureAndLog(operation: String, block: () -> T): T {
        val startTime = System.currentTimeMillis()
        val result = block()
        val duration = System.currentTimeMillis() - startTime
        logCustomTiming(operation, duration)
        return result
    }
}
