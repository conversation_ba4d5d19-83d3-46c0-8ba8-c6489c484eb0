#!/bin/bash

# Cold Start Performance Testing Script
# Comprehensive analysis of Android battery monitoring app startup performance
# Tests complete flow: Android System Splash → SplashActivity → LanguageSelection/StartingActivity → MainActivity

set -e

APP_PACKAGE="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
MAIN_ACTIVITY="$APP_PACKAGE/.activity.main.MainActivity"
SPLASH_ACTIVITY="$APP_PACKAGE/.activity.splash.SplashActivity"

# Use emulator for consistent testing
DEVICE_ID="emulator-5554"
ADB_CMD="adb -s $DEVICE_ID"

# Performance thresholds (in milliseconds)
EXCELLENT_THRESHOLD=250
GOOD_THRESHOLD=500
ACCEPTABLE_THRESHOLD=1000

# Test configuration
NUM_TESTS=5
LOGCAT_BUFFER_SIZE="16M"

echo "=========================================="
echo "Cold Start Performance Analysis"
echo "=========================================="
echo "Package: $APP_PACKAGE"
echo "Device: $DEVICE_ID"
echo "Tests per scenario: $NUM_TESTS"
echo ""

# Function to log with timestamp
log_with_timestamp() {
    echo "[$(date '+%H:%M:%S')] $1"
}

# Function to check if device is connected
check_device() {
    if ! $ADB_CMD shell echo "Device connected" > /dev/null 2>&1; then
        echo "❌ Error: Device $DEVICE_ID not found or not responding"
        echo "Available devices:"
        adb devices
        exit 1
    fi
    log_with_timestamp "✅ Device $DEVICE_ID connected and responsive"
}

# Function to clear app data and cache
clear_app_data() {
    log_with_timestamp "🧹 Clearing app data and cache..."
    $ADB_CMD shell pm clear $APP_PACKAGE > /dev/null 2>&1
    $ADB_CMD shell am force-stop $APP_PACKAGE > /dev/null 2>&1
    sleep 2
}

# Function to setup logcat monitoring
setup_logcat_monitoring() {
    local test_name="$1"
    local logcat_file="/tmp/logcat_${test_name}_$(date +%s).txt"
    
    # Clear logcat buffer
    $ADB_CMD logcat -c
    
    # Start logcat monitoring in background
    $ADB_CMD logcat -v time -s \
        "SplashActivity:D" \
        "LanguageSelectionActivity:D" \
        "StartingActivity:D" \
        "MainActivity:D" \
        "BatteryApplication:D" \
        "STARTUP_TIMING:D" \
        "SPLASH_PROGRESS:D" \
        "LANGUAGE_SELECTION:D" \
        "UI_TIMING:D" \
        "PHASE_3:D" \
        "ActivityManager:I" \
        > "$logcat_file" &
    
    local logcat_pid=$!
    echo "$logcat_file:$logcat_pid"
}

# Function to stop logcat monitoring and analyze results
analyze_logcat_results() {
    local logcat_info="$1"
    local test_name="$2"
    
    IFS=':' read -r logcat_file logcat_pid <<< "$logcat_info"
    
    # Stop logcat monitoring
    kill $logcat_pid 2>/dev/null || true
    sleep 1
    
    if [[ -f "$logcat_file" ]]; then
        log_with_timestamp "📊 Analyzing logcat results for $test_name..."
        
        # Extract key timing metrics
        echo "  🔍 Key Timing Metrics:"
        grep -E "(STARTUP_TIMING|SPLASH_PROGRESS|LANGUAGE_SELECTION)" "$logcat_file" | head -20
        
        # Extract memory usage
        echo "  💾 Memory Usage:"
        grep -E "Memory usage" "$logcat_file" | tail -5
        
        # Extract service startup times
        echo "  ⚙️ Service Startup:"
        grep -E "(Critical services|Non-critical services|battery services)" "$logcat_file" | tail -5
        
        # Clean up logcat file
        rm -f "$logcat_file"
    else
        log_with_timestamp "⚠️ Logcat file not found for $test_name"
    fi
}

# Function to measure cold start time
measure_cold_start() {
    local test_name="$1"
    local activity="$2"
    local iteration="$3"

    log_with_timestamp "🚀 Test $iteration: $test_name"

    # Setup logcat monitoring
    local logcat_info=$(setup_logcat_monitoring "${test_name}_${iteration}")

    # Force stop app
    $ADB_CMD shell am force-stop $APP_PACKAGE > /dev/null 2>&1
    sleep 2

    # Measure startup time using a more compatible approach
    local start_time=$(python3 -c "import time; print(int(time.time() * 1000))" 2>/dev/null || echo $(($(date +%s) * 1000)))
    local adb_result=$($ADB_CMD shell am start -W -n "$activity" 2>&1)
    local end_time=$(python3 -c "import time; print(int(time.time() * 1000))" 2>/dev/null || echo $(($(date +%s) * 1000)))

    local total_time=$((end_time - start_time))

    # Extract ADB timing metrics
    local this_time=$(echo "$adb_result" | grep "ThisTime:" | awk '{print $2}' || echo "N/A")
    local total_time_adb=$(echo "$adb_result" | grep "TotalTime:" | awk '{print $2}' || echo "N/A")
    local wait_time=$(echo "$adb_result" | grep "WaitTime:" | awk '{print $2}' || echo "N/A")

    # Use ADB TotalTime if available and reasonable, otherwise use our measurement
    if [[ "$total_time_adb" != "N/A" && "$total_time_adb" -gt 0 && "$total_time_adb" -lt 10000 ]]; then
        total_time=$total_time_adb
    fi

    # Determine performance rating
    local rating="❌ POOR"
    if [[ $total_time -le $EXCELLENT_THRESHOLD ]]; then
        rating="🟢 EXCELLENT"
    elif [[ $total_time -le $GOOD_THRESHOLD ]]; then
        rating="🟡 GOOD"
    elif [[ $total_time -le $ACCEPTABLE_THRESHOLD ]]; then
        rating="🟠 ACCEPTABLE"
    fi

    echo "    📏 Total Time: ${total_time}ms $rating"
    echo "    📱 ADB ThisTime: ${this_time}ms"
    echo "    📱 ADB TotalTime: ${total_time_adb}ms"
    echo "    📱 ADB WaitTime: ${wait_time}ms"

    # Wait for app to stabilize before analyzing logs
    sleep 3

    # Analyze logcat results
    analyze_logcat_results "$logcat_info" "${test_name}_${iteration}"

    echo "$total_time"
}

# Function to calculate statistics
calculate_stats() {
    local times=("$@")
    local sum=0
    local count=${#times[@]}
    
    # Calculate sum
    for time in "${times[@]}"; do
        sum=$((sum + time))
    done
    
    # Calculate average
    local avg=$((sum / count))
    
    # Find min and max
    local min=${times[0]}
    local max=${times[0]}
    for time in "${times[@]}"; do
        if [[ $time -lt $min ]]; then
            min=$time
        fi
        if [[ $time -gt $max ]]; then
            max=$time
        fi
    done
    
    echo "avg:$avg min:$min max:$max"
}

# Function to test complete cold start flow
test_complete_cold_start() {
    echo ""
    echo "🔥 Testing Complete Cold Start Flow (First Time User)"
    echo "Flow: SplashActivity → LanguageSelectionActivity → StartingActivity → MainActivity"
    echo ""
    
    local times=()
    
    for i in $(seq 1 $NUM_TESTS); do
        clear_app_data
        local time=$(measure_cold_start "Complete_Cold_Start" "$SPLASH_ACTIVITY" "$i")
        times+=($time)
        
        if [[ $i -lt $NUM_TESTS ]]; then
            log_with_timestamp "⏳ Waiting 5 seconds before next test..."
            sleep 5
        fi
    done
    
    local stats=$(calculate_stats "${times[@]}")
    IFS=':' read -r avg min max <<< "$stats"
    
    echo ""
    echo "📊 Complete Cold Start Results:"
    echo "   Average: ${avg}ms"
    echo "   Best: ${min}ms"
    echo "   Worst: ${max}ms"
    echo "   Tests: ${times[*]}"
    
    return $avg
}

# Function to test returning user cold start
test_returning_user_cold_start() {
    echo ""
    echo "🔄 Testing Returning User Cold Start"
    echo "Flow: SplashActivity → MainActivity (onboarding completed)"
    echo ""
    
    # Setup app for returning user (complete onboarding first)
    log_with_timestamp "🛠️ Setting up returning user state..."
    clear_app_data
    
    # Launch app and complete onboarding flow manually
    $ADB_CMD shell am start -n "$SPLASH_ACTIVITY" > /dev/null 2>&1
    sleep 10  # Allow time for manual onboarding completion
    
    local times=()
    
    for i in $(seq 1 $NUM_TESTS); do
        # Force stop but don't clear data (preserve onboarding state)
        $ADB_CMD shell am force-stop $APP_PACKAGE > /dev/null 2>&1
        sleep 2
        
        local time=$(measure_cold_start "Returning_User_Cold_Start" "$SPLASH_ACTIVITY" "$i")
        times+=($time)
        
        if [[ $i -lt $NUM_TESTS ]]; then
            log_with_timestamp "⏳ Waiting 3 seconds before next test..."
            sleep 3
        fi
    done
    
    local stats=$(calculate_stats "${times[@]}")
    IFS=':' read -r avg min max <<< "$stats"
    
    echo ""
    echo "📊 Returning User Cold Start Results:"
    echo "   Average: ${avg}ms"
    echo "   Best: ${min}ms"
    echo "   Worst: ${max}ms"
    echo "   Tests: ${times[*]}"
    
    return $avg
}

# Function to test direct MainActivity launch
test_direct_main_activity() {
    echo ""
    echo "🎯 Testing Direct MainActivity Launch"
    echo "Flow: MainActivity (bypassing splash)"
    echo ""
    
    local times=()
    
    for i in $(seq 1 $NUM_TESTS); do
        local time=$(measure_cold_start "Direct_MainActivity" "$MAIN_ACTIVITY" "$i")
        times+=($time)
        
        if [[ $i -lt $NUM_TESTS ]]; then
            log_with_timestamp "⏳ Waiting 3 seconds before next test..."
            sleep 3
        fi
    done
    
    local stats=$(calculate_stats "${times[@]}")
    IFS=':' read -r avg min max <<< "$stats"
    
    echo ""
    echo "📊 Direct MainActivity Results:"
    echo "   Average: ${avg}ms"
    echo "   Best: ${min}ms"
    echo "   Worst: ${max}ms"
    echo "   Tests: ${times[*]}"
    
    return $avg
}

# Main execution
main() {
    check_device
    
    # Test scenarios
    test_complete_cold_start
    local complete_avg=$?
    
    test_returning_user_cold_start
    local returning_avg=$?
    
    test_direct_main_activity
    local direct_avg=$?
    
    # Final summary
    echo ""
    echo "=========================================="
    echo "📈 PERFORMANCE SUMMARY"
    echo "=========================================="
    echo "Complete Cold Start (First Time): ${complete_avg}ms"
    echo "Returning User Cold Start: ${returning_avg}ms"
    echo "Direct MainActivity Launch: ${direct_avg}ms"
    echo ""
    
    # Performance recommendations
    echo "🎯 PERFORMANCE ANALYSIS:"
    if [[ $complete_avg -le $EXCELLENT_THRESHOLD ]]; then
        echo "✅ Complete cold start performance is EXCELLENT"
    elif [[ $complete_avg -le $GOOD_THRESHOLD ]]; then
        echo "🟡 Complete cold start performance is GOOD - minor optimizations possible"
    else
        echo "🔴 Complete cold start performance needs optimization"
    fi
    
    if [[ $returning_avg -le $EXCELLENT_THRESHOLD ]]; then
        echo "✅ Returning user performance is EXCELLENT"
    else
        echo "🟡 Returning user performance could be improved"
    fi
    
    echo ""
    echo "📝 Test completed at $(date)"
}

# Run main function
main "$@"
